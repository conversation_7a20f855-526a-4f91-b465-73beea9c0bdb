# These requirements include all dependencies for all top-level python scripts
# for llama.cpp. Avoid adding packages here directly.
#
# Package versions must stay compatible across all top-level python scripts.
#

-r 3rdparty/llama.cpp/requirements/requirements-convert_legacy_llama.txt
-r 3rdparty/llama.cpp/requirements/requirements-convert_hf_to_gguf.txt
-r 3rdparty/llama.cpp/requirements/requirements-convert_hf_to_gguf_update.txt
-r 3rdparty/llama.cpp/requirements/requirements-convert_llama_ggml_to_gguf.txt
-r 3rdparty/llama.cpp/requirements/requirements-convert_lora_to_gguf.txt