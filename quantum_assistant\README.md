# Quantum Protocol Engineering Assistant

A lightweight web application that allows quantum researchers to describe quantum error correction protocols in natural language and automatically generate Qiskit code for implementation and testing.

## Features

- **Natural Language Input**: Describe quantum protocols in plain English
- **Local Inference**: Uses BitNet for local inference without requiring a GPU
- **Qudit Support**: Specialized in qudit-based quantum error correction protocols
- **Ready-to-Run Code**: Generates complete Qiskit scripts that can be executed immediately
- **Template Fallback**: Uses pre-defined templates when BitNet inference is unavailable

## Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/quantum-protocol-assistant.git
   cd quantum-protocol-assistant
   ```

2. Install the required dependencies:
   ```bash
   pip install -r requirements_app.txt
   ```

3. (Optional) Download the BitNet model:
   - Download the model from Hugging Face: [microsoft/bitnet-b1.58-2B-4T](https://huggingface.co/microsoft/bitnet-b1.58-2B-4T)
   - Place the model in the `models/bitnet_b1_58-3B/` directory
   - Rename the model file to `ggml-model-i2_s.gguf`

## Usage

1. Run the application:
   ```bash
   python run_quantum_assistant.py
   ```

2. Open your web browser and navigate to:
   ```
   http://localhost:8501
   ```

3. Enter a description of your quantum protocol in the text area
4. Click "Generate Qiskit Code"
5. View the generated code and download it as a Python file
6. Run the downloaded file with Python to execute the quantum circuit

## Example Prompts

- "Implement Shor's 9-qubit code for quantum error correction"
- "Create a 3-level qutrit simulator with X and Z gates"
- "Design a quantum error correction code for phase errors in qutrits"

## How It Works

1. **Input Processing**: The application takes a natural language description of a quantum protocol
2. **Inference**: BitNet processes the description to understand the required quantum operations
3. **Code Generation**: The system generates Qiskit code based on the inference results
4. **Template Fallback**: If BitNet inference fails, the system falls back to template-based generation
5. **Output**: The generated code is displayed and made available for download

## Requirements

- Python 3.8+
- Streamlit
- Qiskit
- Jinja2
- NumPy
- BitNet (optional, for local inference)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [BitNet](https://github.com/microsoft/BitNet) for the 1-bit inference framework
- [Qiskit](https://qiskit.org/) for the quantum computing framework
- [Streamlit](https://streamlit.io/) for the web application framework
