import os
import sys
import streamlit as st
import base64
from pathlib import Path

# Add the parent directory to the path to access BitNet modules
sys.path.append(str(Path(__file__).parent.parent))

# Import our modules
from quantum_assistant.infer import BitNetInference
from quantum_assistant.utils import Template<PERSON><PERSON><PERSON>, select_template_for_description, clean_code, generate_filename

# Set page configuration
st.set_page_config(
    page_title="Quantum Protocol Engineering Assistant",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1E88E5;
        text-align: center;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #0D47A1;
        margin-bottom: 1rem;
    }
    .info-text {
        font-size: 1rem;
        color: #424242;
    }
    .code-header {
        font-size: 1.2rem;
        color: #0D47A1;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }
    .stButton>button {
        background-color: #1E88E5;
        color: white;
        font-weight: bold;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        width: 100%;
    }
    .stTextArea>div>div>textarea {
        font-family: monospace;
    }
</style>
""", unsafe_allow_html=True)

def get_download_link(code, filename):
    """
    Generate a download link for the generated code.
    
    Args:
        code (str): The generated code
        filename (str): The filename for the download
        
    Returns:
        str: HTML for the download link
    """
    b64 = base64.b64encode(code.encode()).decode()
    href = f'<a href="data:file/txt;base64,{b64}" download="{filename}" style="text-decoration:none;">Download {filename}</a>'
    return href

def main():
    # Header
    st.markdown('<h1 class="main-header">Quantum Protocol Engineering Assistant</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <p class="info-text">
    This tool helps quantum researchers describe quantum error correction protocols in natural language 
    and automatically generates Qiskit code for implementation and testing.
    </p>
    """, unsafe_allow_html=True)
    
    # Sidebar with information
    with st.sidebar:
        st.markdown('<h2 class="sub-header">About</h2>', unsafe_allow_html=True)
        st.markdown("""
        The Quantum Protocol Engineering Assistant uses BitNet to process natural language descriptions 
        of quantum protocols and generate executable Qiskit code.
        
        **Features:**
        - Local inference with BitNet
        - Support for qudit-based protocols
        - Error correction code generation
        - Ready-to-run Qiskit scripts
        
        **Example Prompts:**
        - "Implement Shor's 9-qubit code for quantum error correction"
        - "Create a 3-level qutrit simulator with X and Z gates"
        - "Design a quantum error correction code for phase errors in qutrits"
        """)
        
        st.markdown('<h2 class="sub-header">Settings</h2>', unsafe_allow_html=True)
        
        # Model settings
        model_path = st.text_input(
            "BitNet Model Path",
            value="models/bitnet_b1_58-3B/ggml-model-i2_s.gguf",
            help="Path to the BitNet model file"
        )
        
        max_tokens = st.slider(
            "Max Tokens",
            min_value=256,
            max_value=2048,
            value=1024,
            step=128,
            help="Maximum number of tokens to generate"
        )
        
        temperature = st.slider(
            "Temperature",
            min_value=0.1,
            max_value=1.0,
            value=0.2,
            step=0.1,
            help="Temperature for generation (higher = more creative, lower = more deterministic)"
        )
        
        use_templates = st.checkbox(
            "Use Templates",
            value=True,
            help="Use pre-defined templates when BitNet inference fails or is unavailable"
        )
    
    # Main content
    st.markdown('<h2 class="sub-header">Protocol Description</h2>', unsafe_allow_html=True)
    
    # Protocol description input
    protocol_description = st.text_area(
        "Describe your quantum protocol in natural language",
        height=150,
        placeholder="Example: Implement a 3-qudit quantum error correction code that can correct phase errors"
    )
    
    # Initialize session state for generated code
    if 'generated_code' not in st.session_state:
        st.session_state.generated_code = ""
    
    if 'filename' not in st.session_state:
        st.session_state.filename = "quantum_protocol.py"
    
    # Generate code button
    if st.button("Generate Qiskit Code"):
        if not protocol_description:
            st.error("Please enter a protocol description.")
        else:
            with st.spinner("Generating Qiskit code..."):
                try:
                    # Initialize BitNet inference
                    inference = BitNetInference(model_path=model_path)
                    
                    # Generate code using BitNet
                    generated_code = inference.generate_code(
                        protocol_description,
                        max_tokens=max_tokens,
                        temperature=temperature
                    )
                    
                    # Clean up the generated code
                    generated_code = clean_code(generated_code)
                    
                    # If BitNet inference failed or use_templates is enabled
                    if not generated_code or use_templates:
                        # Use template-based generation as fallback
                        template_name = select_template_for_description(protocol_description)
                        template_manager = TemplateManager()
                        template_code = template_manager.render_template(template_name)
                        
                        # If BitNet failed completely, use the template
                        if not generated_code:
                            generated_code = template_code
                    
                    # Generate a filename based on the description
                    filename = generate_filename(protocol_description)
                    
                    # Store in session state
                    st.session_state.generated_code = generated_code
                    st.session_state.filename = filename
                    
                    st.success("Code generated successfully!")
                    
                except Exception as e:
                    st.error(f"Error generating code: {str(e)}")
                    
                    # Use template as fallback
                    if use_templates:
                        template_name = select_template_for_description(protocol_description)
                        template_manager = TemplateManager()
                        generated_code = template_manager.render_template(template_name)
                        filename = generate_filename(protocol_description)
                        
                        st.session_state.generated_code = generated_code
                        st.session_state.filename = filename
                        
                        st.warning("Used template-based generation as fallback.")
    
    # Display generated code
    if st.session_state.generated_code:
        st.markdown('<h2 class="sub-header">Generated Qiskit Code</h2>', unsafe_allow_html=True)
        
        # Code display
        st.code(st.session_state.generated_code, language="python")
        
        # Download button
        st.markdown(
            get_download_link(st.session_state.generated_code, st.session_state.filename),
            unsafe_allow_html=True
        )
        
        # Execution instructions
        st.markdown('<h3 class="code-header">How to Run This Code</h3>', unsafe_allow_html=True)
        st.markdown("""
        1. Download the generated Python file
        2. Make sure you have Qiskit installed: `pip install qiskit qiskit-aer`
        3. Run the file: `python {}` 
        4. The code will simulate the quantum circuit and display results
        """.format(st.session_state.filename))

if __name__ == "__main__":
    main()
