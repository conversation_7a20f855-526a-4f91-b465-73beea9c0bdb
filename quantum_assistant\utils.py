import os
import re
import jinja2
from pathlib import Path

class TemplateManager:
    """
    Class to manage Qiskit code templates for quantum protocols.
    """
    def __init__(self, templates_dir=None):
        """
        Initialize the template manager.

        Args:
            templates_dir (str, optional): Path to the templates directory.
                If None, uses the default templates directory.
        """
        if templates_dir is None:
            templates_dir = os.path.join(os.path.dirname(__file__), 'templates')

        self.templates_dir = templates_dir
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(templates_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )

    def get_template_names(self):
        """
        Get a list of available template names.

        Returns:
            list: List of template names
        """
        template_files = [f for f in os.listdir(self.templates_dir)
                         if f.endswith('.template')]
        return [os.path.splitext(f)[0] for f in template_files]

    def render_template(self, template_name, **kwargs):
        """
        Render a template with the given parameters.

        Args:
            template_name (str): Name of the template to render
            **kwargs: Parameters to pass to the template

        Returns:
            str: Rendered template
        """
        template_path = f"{template_name}.py.template"
        try:
            template = self.env.get_template(template_path)
            return template.render(**kwargs)
        except jinja2.exceptions.TemplateNotFound:
            # If template not found, try to load the file directly
            try:
                with open(os.path.join(self.templates_dir, template_path), 'r') as f:
                    return f.read()
            except FileNotFoundError:
                return f"Template {template_name} not found."

def select_template_for_description(description):
    """
    Select the most appropriate template based on the protocol description.

    Args:
        description (str): Natural language description of the quantum protocol

    Returns:
        str: Name of the selected template
    """
    description = description.lower()

    # Check for specific keywords to determine the template
    if "shor" in description or "9-qubit" in description:
        return "shor_code"
    elif ("qudit" in description or "qutrit" in description) and ("error" in description or "correction" in description or "phase error" in description):
        return "qudit_error_correction"
    elif "qudit" in description or "qutrit" in description:
        return "qudit_simulator"
    else:
        return "shor_code"  # Default to Shor code as a fallback

def clean_code(code):
    """
    Clean up generated code by removing unnecessary markers and fixing formatting.

    Args:
        code (str): Generated code

    Returns:
        str: Cleaned code
    """
    # Remove code block markers if present
    code = re.sub(r'^```python\s*', '', code)
    code = re.sub(r'\s*```$', '', code)

    # Ensure proper imports
    if "import numpy" not in code and "import np" not in code:
        code = "import numpy as np\n" + code

    if "from qiskit import" not in code:
        code = "from qiskit import QuantumCircuit, QuantumRegister, ClassicalRegister, Aer, execute\n" + code

    return code

def generate_filename(description):
    """
    Generate a filename based on the protocol description.

    Args:
        description (str): Natural language description of the quantum protocol

    Returns:
        str: Generated filename
    """
    # Extract key words from the description
    words = re.findall(r'\b\w+\b', description.lower())

    # Filter out common words
    stop_words = {'a', 'an', 'the', 'and', 'or', 'for', 'in', 'on', 'with', 'to', 'of'}
    keywords = [word for word in words if word not in stop_words]

    # Use up to 5 keywords for the filename
    if len(keywords) > 5:
        keywords = keywords[:5]

    # Join keywords with underscores
    filename = '_'.join(keywords)

    # Add .py extension
    if not filename:
        filename = "quantum_protocol"

    return f"{filename}.py"
