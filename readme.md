<div align="center">
  <img src="logo.png" alt="Quantum Protocol Engineering Assistant Logo" width="200"/>
  
  # Quantum Protocol Engineering Assistant
  
  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Qiskit](https://img.shields.io/badge/Qiskit-0.41.0-blue.svg)](https://qiskit.org/)
  [![Python](https://img.shields.io/badge/Python-3.8+-brightgreen.svg)](https://www.python.org/)
  [![Streamlit](https://img.shields.io/badge/Streamlit-1.24.0-red.svg)](https://streamlit.io/)
</div>

## 📋 Sumário Executivo

O **Quantum Protocol Engineering Assistant** é uma aplicação web leve que permite aos pesquisadores quânticos descrever protocolos de correção de erros quânticos baseados em qudits usando linguagem natural. A ferramenta gera automaticamente esqueletos de código Qiskit prontos para testes.

A aplicação utiliza:
- **BitNet quantizado** para processar descrições complexas em tempo real em CPU
- **Streamlit** para prototipagem rápida de interface
- **Qiskit** para geração de scripts Python compatíveis com simuladores ou hardware quântico real

## 🏗️ Arquitetura

### 🔄 Inferência BitNet

- **BitNet b1.58** é um framework de inferência de 1-bit que executa LLMs quantizados sem perda perceptível de qualidade
- Opera a ~6 tokens/s em CPUs x86 comuns
- Processa descrições de linguagem natural e gera código Qiskit

### 🧩 Geração de Código Qiskit

- **Qiskit** fornece bibliotecas para construção e simulação de circuitos quânticos
- O assistente preenche templates de protocolos (ex: circuitos de correção baseados em qudits)
- Gera scripts prontos para execução

### 🖥️ Interface Streamlit

- **Streamlit** cria uma UI interativa com código Python mínimo
- Inclui caixas de texto, botões e downloads de arquivos
- Interface simples para entrada de descrições e download do código gerado

## ✨ Funcionalidades do MVP

| Funcionalidade | Descrição |
|----------------|-----------|
| **Entrada de Protocolo** | Campo de texto livre para descrição em linguagem natural dos protocolos quânticos |
| **Inferência Local** | Chamada de subprocesso Python para BitNet gerar código Qiskit com base na descrição |
| **Geração de Script** | Montagem de um esqueleto de script Python contendo imports, criação de circuito, codificação de erro e medições |
| **Download de Código** | Botão para baixar o arquivo gerado para execução local ou implantação em nuvem |

## 🛠️ Implementação

A implementação consiste nos seguintes componentes:

```
quantum_assistant/
├── app.py            # Aplicação principal Streamlit
├── infer.py          # Módulo de inferência BitNet
├── utils.py          # Funções utilitárias para manipulação de templates
├── templates/        # Diretório contendo templates de código Qiskit
└── logo.png          # Logo da aplicação
run_quantum_assistant.py  # Script para executar a aplicação
```

## 🚀 Como Usar

1. **Instale as dependências necessárias:**
   ```bash
   pip install -r requirements_app.txt
   ```

2. **(Opcional) Baixe o modelo BitNet:**
   - Baixe do Hugging Face: `microsoft/bitnet-b1.58-2B-4T`
   - Coloque no diretório `models/bitnet_b1_58-3B/`
   - Renomeie para `ggml-model-i2_s.gguf`

3. **Execute a aplicação:**
   ```bash
   python run_quantum_assistant.py
   ```

4. **Abra seu navegador e acesse:**
   ```
   http://localhost:8501
   ```

5. **Insira uma descrição do seu protocolo quântico e clique em "Gerar Código Qiskit"**

6. **Baixe e execute o arquivo Python gerado**

## 💡 Exemplos de Prompts

- "Implementar o código de 9 qubits de Shor para correção de erros quânticos"
- "Criar um simulador de qutrit de 3 níveis com portas X e Z"
- "Projetar um código de correção de erros quânticos para erros de fase em qutrits"

## 🔄 Mecanismo de Fallback

Se a inferência do BitNet falhar ou o modelo não estiver disponível, a aplicação recorre à geração baseada em templates usando modelos predefinidos para protocolos quânticos comuns.

## 🔮 Melhorias Futuras

1. **Suporte a Protocolos Avançados**: Adicionar mais templates para protocolos quânticos especializados
2. **Visualização de Circuitos**: Integrar com as ferramentas de visualização do Qiskit
3. **Execução em Nuvem**: Adicionar opção para executar código gerado no IBM Quantum Experience
4. **Treinamento de Modelo Personalizado**: Treinar um modelo especializado para geração de protocolos quânticos

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - consulte o arquivo LICENSE para obter detalhes.
