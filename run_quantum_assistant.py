import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import streamlit
        import qiskit
        import jinja2
        import numpy
        print("All required dependencies are installed.")
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please install required dependencies with: pip install -r requirements_app.txt")
        return False

def check_model(model_path):
    """Check if the BitNet model exists."""
    if os.path.exists(model_path):
        print(f"BitNet model found at: {model_path}")
        return True
    else:
        print(f"BitNet model not found at: {model_path}")
        print("You can download the model from Hugging Face: microsoft/bitnet-b1.58-2B-4T")
        print("Or use the fallback template-based generation.")
        return False

def run_streamlit_app(port=8501):
    """Run the Streamlit application."""
    app_path = os.path.join("quantum_assistant", "app.py")
    
    if not os.path.exists(app_path):
        print(f"Error: Application file not found at {app_path}")
        return False
    
    try:
        cmd = [
            "streamlit", "run", app_path,
            "--server.port", str(port),
            "--browser.serverAddress", "localhost",
            "--server.headless", "false",
        ]
        
        print(f"Starting Quantum Protocol Engineering Assistant on port {port}...")
        subprocess.run(cmd)
        return True
    except Exception as e:
        print(f"Error running Streamlit application: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run Quantum Protocol Engineering Assistant")
    parser.add_argument("--port", type=int, default=8501, help="Port to run the Streamlit app on")
    parser.add_argument("--model", type=str, default="models/bitnet_b1_58-3B/ggml-model-i2_s.gguf", 
                        help="Path to BitNet model")
    
    args = parser.parse_args()
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check model (just a warning, app will use templates if model not found)
    check_model(args.model)
    
    # Run the Streamlit app
    run_streamlit_app(port=args.port)

if __name__ == "__main__":
    main()
