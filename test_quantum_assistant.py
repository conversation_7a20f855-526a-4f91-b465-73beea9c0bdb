import os
import sys
from pathlib import Path

# Add the parent directory to the path to access modules
sys.path.append(str(Path(__file__).parent))

from quantum_assistant.infer import BitNetInference
from quantum_assistant.utils import Template<PERSON><PERSON><PERSON>, select_template_for_description, clean_code, generate_filename

def test_template_selection():
    """Test the template selection logic."""
    print("Testing template selection...")
    
    # Test cases
    test_cases = [
        ("Implement Shor's 9-qubit code for quantum error correction", "shor_code"),
        ("Create a 3-level qutrit simulator with X and Z gates", "qudit_simulator"),
        ("Design a quantum error correction code for phase errors in qutrits", "qudit_error_correction"),
        ("Random description that doesn't match any specific template", "shor_code"),  # Default
    ]
    
    for description, expected_template in test_cases:
        template = select_template_for_description(description)
        result = "✓" if template == expected_template else "✗"
        print(f"{result} Description: '{description}' -> Template: '{template}' (Expected: '{expected_template}')")

def test_template_rendering():
    """Test the template rendering functionality."""
    print("\nTesting template rendering...")
    
    template_manager = TemplateManager()
    template_names = template_manager.get_template_names()
    
    print(f"Available templates: {template_names}")
    
    for template_name in template_names:
        code = template_manager.render_template(template_name)
        if code and "Template not found" not in code:
            print(f"✓ Successfully rendered template: {template_name}")
        else:
            print(f"✗ Failed to render template: {template_name}")

def test_filename_generation():
    """Test the filename generation logic."""
    print("\nTesting filename generation...")
    
    # Test cases
    test_cases = [
        "Implement Shor's 9-qubit code for quantum error correction",
        "Create a 3-level qutrit simulator with X and Z gates",
        "Design a quantum error correction code for phase errors in qutrits",
        "",  # Empty string
    ]
    
    for description in test_cases:
        filename = generate_filename(description)
        print(f"Description: '{description}' -> Filename: '{filename}'")

def test_bitnet_inference():
    """Test the BitNet inference functionality."""
    print("\nTesting BitNet inference...")
    
    # Initialize BitNet inference
    inference = BitNetInference()
    
    # Check if model exists
    model_path = inference.model_path
    if os.path.exists(model_path):
        print(f"✓ BitNet model found at: {model_path}")
        
        # Test inference with a simple description
        description = "Implement a simple quantum error correction code"
        print(f"Generating code for: '{description}'")
        
        try:
            code = inference.generate_code(description, max_tokens=256)
            if code:
                print(f"✓ Successfully generated code ({len(code)} characters)")
                print("\nFirst few lines of generated code:")
                print("\n".join(code.split("\n")[:5]) + "\n...")
            else:
                print("✗ Failed to generate code")
        except Exception as e:
            print(f"✗ Error during inference: {e}")
    else:
        print(f"✗ BitNet model not found at: {model_path}")
        print("Skipping inference test. Using fallback generation instead.")
        
        # Test fallback generation
        description = "Implement a simple quantum error correction code"
        print(f"Using fallback generation for: '{description}'")
        
        code = inference._fallback_generation(description)
        if code:
            print(f"✓ Successfully generated fallback code ({len(code)} characters)")
            print("\nFirst few lines of generated code:")
            print("\n".join(code.split("\n")[:5]) + "\n...")
        else:
            print("✗ Failed to generate fallback code")

if __name__ == "__main__":
    print("Running Quantum Assistant tests...\n")
    
    test_template_selection()
    test_template_rendering()
    test_filename_generation()
    test_bitnet_inference()
    
    print("\nTests completed.")
